// contexts/AuthContext.tsx - FIXED VERSION
'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabaseClient } from '@/lib/supabase-client'
import type { Database } from '@/types/supabase'

interface UserRole {
  isAdmin: boolean
  isPelatis: boolean
  roles: string[]
  pelatisId: string | null
}

interface AuthContextType {
  user: User | null
  session: Session | null
  userRole: UserRole
  loading: boolean
  signOut: () => Promise<void>
  refreshUserRole: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [userRole, setUserRole] = useState<UserRole>({
    isAdmin: false,
    isPelatis: false,
    roles: [],
    pelatisId: null
  })
  const [loading, setLoading] = useState(true)

  const fetchUserRole = async (userId: string): Promise<UserRole> => {
    console.log('🔍 fetchUserRole called with userId:', userId)
    
    try {
      // Parallel fetch for roles and pelatis data
      const [rolesResult, pelatesResult] = await Promise.allSettled([
        supabaseClient.rpc('getUserRoles', { p_user_id: userId }),
        supabaseClient.from('pelates').select('id').eq('auth_user_id', userId).maybeSingle()
      ])

      console.log('📊 Roles RPC result:', rolesResult)
      console.log('👤 Pelates result:', pelatesResult)

      let roles: string[] = []
      let pelatisId: string | null = null

      // Handle roles result
      if (rolesResult.status === 'fulfilled' && !rolesResult.value.error) {
        roles = Array.isArray(rolesResult.value.data) ? rolesResult.value.data : []
        console.log('✅ Roles from RPC:', roles)
      }

      // Handle pelatis result
      if (pelatesResult.status === 'fulfilled' && !pelatesResult.value.error && pelatesResult.value.data) {
        pelatisId = pelatesResult.value.data.id
        console.log('✅ Pelatis ID fetched:', pelatisId)
      }

      const result = {
        isAdmin: roles.includes('admin'),
        isPelatis: !!pelatisId,
        roles,
        pelatisId
      }

      console.log('🎯 Final user role result:', result)
      return result
    } catch (error) {
      console.error('💥 Error fetching user roles:', error)
      return { isAdmin: false, isPelatis: false, roles: [], pelatisId: null }
    }
  }

  const refreshUserRole = async () => {
    if (user) {
      const roles = await fetchUserRole(user.id)
      setUserRole(roles)
    }
  }

  const signOut = async () => {
    try {
      await supabaseClient.auth.signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  useEffect(() => {
    console.log('🎬 AuthContext useEffect started - FIXED VERSION')
    let mounted = true

    // ✅ REMOVED: The problematic getSession() call
    // ❌ OLD CODE: const { data: { session: initialSession }, error } = await supabaseClient.auth.getSession()
    
    // ✅ NEW APPROACH: Only set up the auth listener, let it handle initial state
    console.log('🔗 Setting up auth state change listener (no initial getSession)...')
    
    const { data: { subscription } } = supabaseClient.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth state changed:', event, 'userId:', session?.user?.id)
        
        if (!mounted) {
          console.log('⚠️ Component unmounted, ignoring auth change')
          return
        }

        // Update session and user state
        setSession(session)
        setUser(session?.user ?? null)

        if (session?.user) {
          // Fetch roles for any auth event that provides a user
          console.log('🔄 Fetching roles for user:', session.user.id)
          const roles = await fetchUserRole(session.user.id)
          if (mounted) {
            console.log('✅ Setting roles from auth change:', roles)
            setUserRole(roles)
          }
        } else {
          console.log('❌ No user, resetting roles')
          setUserRole({ isAdmin: false, isPelatis: false, roles: [], pelatisId: null })
        }

        // Set loading to false after first auth event
        if (mounted) {
          console.log('✅ Setting loading to false after auth state change')
          setLoading(false)
        }
      }
    )

    // ✅ TEST: Check if user exists immediately (outside the auth listener)
    console.log('🧪 Testing manual auth check...')
    supabaseClient.auth.getUser().then(({ data: { user }, error }) => {
      console.log('🔍 Manual getUser result:', { hasUser: !!user, userId: user?.id, error })
      
      // If we have a user but no auth events fired, manually trigger the auth flow
      if (user && !session && mounted) {
        console.log('🔧 Found user but no session - manually setting state')
        setUser(user)
        // Also try to get the session
        supabaseClient.auth.getSession().then(({ data: { session: manualSession }, error: sessionError }) => {
          console.log('🔍 Manual getSession result:', { hasSession: !!manualSession, error: sessionError })
          if (manualSession && mounted) {
            setSession(manualSession)
            // Fetch roles
            fetchUserRole(user.id).then(roles => {
              if (mounted) {
                setUserRole(roles)
                setLoading(false)
              }
            })
          }
        })
      }
    })

    // ✅ NEW: Set a timeout to ensure loading gets set to false even if no auth events fire
    const timeoutId = setTimeout(() => {
      if (mounted) {
        console.log('⏰ Timeout reached, setting loading to false')
        setLoading(false)
      }
    }, 2000) // 2 second timeout

    // Cleanup function
    return () => {
      console.log('🧹 AuthContext cleanup - unmounting')
      mounted = false
      clearTimeout(timeoutId)
      subscription.unsubscribe()
    }
  }, []) // Empty dependency array - this effect should only run once

  const value: AuthContextType = {
    user,
    session,
    userRole,
    loading,
    signOut,
    refreshUserRole,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}