// lib/supabase-client.ts - FIXED VERSION WITH DEBUG
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import type { Database } from '@/types/supabase'

// Debug environment variables
console.log('🔧 Supabase Client Init - Environment Check:', {
  hasUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
  hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  urlPreview: process.env.NEXT_PUBLIC_SUPABASE_URL?.slice(0, 20) + '...',
})

// Validate environment variables
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
}

if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
}

// Single client instance - created once and reused everywhere
let supabaseClientInstance: ReturnType<typeof createClientComponentClient<Database>> | null = null

export const getSupabaseClient = () => {
  if (!supabaseClientInstance) {
    console.log('🏗️ Creating new Supabase client instance...')
    
    try {
      supabaseClientInstance = createClientComponentClient<Database>({
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
        supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      })
      
      console.log('✅ Supabase client created successfully')
      
      // Test the client immediately
      const authTest = supabaseClientInstance.auth
      console.log('🔑 Auth client exists:', !!authTest)
      
    } catch (error) {
      console.error('💥 Failed to create Supabase client:', error)
      throw error
    }
  }
  
  return supabaseClientInstance
}

// Export the client instance
export const supabaseClient = getSupabaseClient()

// Also export as 'supabase' for compatibility
export const supabase = getSupabaseClient()

// Test the client connection on module load
try {
  const testClient = getSupabaseClient()
  console.log('🧪 Client test on module load:', {
    hasAuth: !!testClient.auth,
    hasFrom: !!testClient.from,
    clientString: testClient.toString().slice(0, 50) + '...'
  })
} catch (error) {
  console.error('💥 Client test failed on module load:', error)
}

// Server components can still create their own clients
export { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
export { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
export { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'