// components/invoices/GreekInvoicePreview.tsx
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Printer, Download } from 'lucide-react';

// Types (same as your existing types)
interface InvoiceData {
  id: string;
  invoice_series: string;
  invoice_number: string;
  issue_date: string;
  client_name: string;
  client_vat: string;
  client_country: string;
  invoice_type: string;
  currency: string;
  total_net: number;
  total_vat: number;
  total_gross: number;
  mark?: string | null;
  qr_url?: string | null;
  created_at: string;
  updated_at: string;
  status: string;
}

interface InvoiceLine {
  id: string;
  invoice_id: string;
  line_number: number;
  description: string;
  quantity: number;
  unit_price: number;
  net_value: number;
  vat_category: number;
  vat_amount: number;
  income_classification_type: string;
  income_classification_category: string;
}

interface PaymentMethod {
  id: string;
  invoice_id: string;
  payment_type: number;
  amount: number;
  payment_info?: string | null;
}

interface CompanySettings {
  id: string;
  companyName: string;
  vatNumber: string;
  country: string;
  branch: string;
  address?: string;
  postalCode?: string;
  city?: string;
  profession?: string;
  taxOffice?: string;
  defaultClassificationType: string;
  defaultClassificationCategory: string;
}

interface GreekInvoicePreviewProps {
  invoice: InvoiceData;
  lines: InvoiceLine[];
  paymentMethods: PaymentMethod[];
  companySettings: CompanySettings;
  onClose?: () => void;
}

// VAT rate mapping
const VAT_RATES: Record<number, string> = {
  1: '24%',
  2: '13%', 
  3: '6%',
  4: '17%',
  5: '9%',
  6: '4%',
  7: '0%',
  8: '0%'
};

// Payment method mapping
const PAYMENT_METHODS: Record<number, string> = {
  1: 'Μετρητά',
  2: 'Επιταγή', 
  3: 'Τραπεζικό έμβασμα',
  4: 'Πιστωτική κάρτα',
  7: 'POS / e-POS'
};

// Format currency for Greek locale
const formatCurrency = (amount: number): string => {
  return amount.toFixed(2).replace('.', ',');
};

// Format date for Greek locale
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('el-GR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch (error) {
    return dateString;
  }
};

export default function GreekInvoicePreview({
  invoice,
  lines,
  paymentMethods,
  companySettings,
  onClose
}: GreekInvoicePreviewProps) {
  
  // Get primary payment method
  const primaryPayment = paymentMethods?.[0];
  const paymentMethodText = primaryPayment 
    ? PAYMENT_METHODS[primaryPayment.payment_type] || `Τύπος ${primaryPayment.payment_type}`
    : 'POS / e-POS';

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadPDF = () => {
    // Use browser's print to PDF functionality
    if (window.navigator.userAgent.indexOf('Chrome') > -1) {
      // Chrome has better print to PDF support
      window.print();
    } else {
      // Fallback: suggest using print dialog
      alert('Παρακαλώ χρησιμοποιήστε το Print Dialog για να αποθηκεύσετε ως PDF');
      window.print();
    }
  };

  return (
    <div className="greek-invoice-container">
      {/* Action buttons - hidden in print */}
      <div className="no-print mb-4 flex justify-between items-center">
        <h2 className="text-xl font-bold">
          Παραστατικό {invoice.invoice_series}-{invoice.invoice_number}
        </h2>
        <div className="flex gap-2">
          <Button onClick={handlePrint} variant="outline">
            <Printer className="h-4 w-4 mr-2" />
            Εκτύπωση
          </Button>
          <Button onClick={handleDownloadPDF}>
            <Download className="h-4 w-4 mr-2" />
            Αποθήκευση PDF
          </Button>
          {onClose && (
            <Button onClick={onClose} variant="outline">
              Κλείσιμο
            </Button>
          )}
        </div>
      </div>

      {/* Invoice Document */}
      <div className="greek-invoice">
        {/* Company Information Header */}
        <div className="company-header">
          <div className="info-row">
            <div className="info-label">Επωνυμία</div>
            <div className="info-value">{companySettings.companyName}</div>
          </div>
          <div className="info-row">
            <div className="info-label">Α.Φ.Μ.</div>
            <div className="info-value">{companySettings.vatNumber}</div>
          </div>
          <div className="info-row">
            <div className="info-label">Επάγγελμα</div>
            <div className="info-value">
              {companySettings.profession || 'Υπηρεσίες Γυμναστηρίου'}
            </div>
          </div>
          <div className="info-row">
            <div className="info-label">Δ.Ο.Υ.</div>
            <div className="info-value">
              {companySettings.taxOffice || 'ΚΑΛΑΜΑΡΙΑΣ'}
            </div>
          </div>
          <div className="info-row">
            <div className="info-label">Διεύθυνση</div>
            <div className="info-value">
              {companySettings.address && companySettings.city 
                ? `${companySettings.address} - ${companySettings.city}, Τ.Κ: ${companySettings.postalCode || ''}`
                : 'ΛΕΟΦΩΡΟΣ ΘΕΣΣΑΛΟΝΙΚΗΣ ΝΗΧΑΝΙΩΝΑΣ 30 - ΠΕΡΑΙΑ, Τ.Κ: 57019'
              }
            </div>
          </div>
        </div>

        {/* Invoice Type Header */}
        <div className="invoice-type-header">
          ΑΠΥ (Απόδειξη Παροχής Υπηρεσιών)
        </div>

        {/* Invoice Details Row */}
        <div className="invoice-details">
          <div className="detail-item">
            <span className="detail-label">Σειρά</span>
            <div className="detail-value">{invoice.invoice_series || '-'}</div>
          </div>
          <div className="detail-item">
            <span className="detail-label">Α.Α.</span>
            <div className="detail-value">{invoice.invoice_number}</div>
          </div>
          <div className="detail-item">
            <span className="detail-label">Ημερομηνία</span>
            <div className="detail-value">{formatDate(invoice.issue_date)}</div>
          </div>
          <div className="detail-item">
            <span className="detail-label">ΜΑΡΚ</span>
            <div className="detail-value">
              {invoice.mark ? invoice.mark.substring(0, 15) : '-'}
            </div>
          </div>
          <div className="detail-item">
            <span className="detail-label">Τρόπος Πληρωμής</span>
            <div className="detail-value">{paymentMethodText}</div>
          </div>
        </div>

        {/* Client Details */}
        <div className="client-section">
          <div className="client-header">Στοιχεία Πελάτη</div>
          <div className="client-info">
            <div className="client-row">
              <span className="client-label">Α.Φ.Μ.:</span>
              <div className="client-value"></div>
            </div>
            <div className="client-row">
              <span className="client-label">Επωνυμία:</span>
              <div className="client-value">{invoice.client_name}</div>
            </div>
            <div className="client-row">
              <span className="client-label">Διεύθυνση:</span>
              <div className="client-value">
                - {companySettings.city || 'ΠΕΡΑΙΑ'} {companySettings.postalCode || '57010'}
              </div>
            </div>
          </div>
        </div>

        {/* Invoice Lines Table */}
        <div className="invoice-table">
          <div className="table-header">
            <div className="col-code">Κωδ.</div>
            <div className="col-description">Περιγραφή</div>
            <div className="col-quantity">Ποσότητα</div>
            <div className="col-unit">Μ.Μ.</div>
            <div className="col-price">Τιμή (€)</div>
            <div className="col-discount">Έκπτωση (€)</div>
            <div className="col-value">Αξία (€)</div>
            <div className="col-vat-percent">ΦΠΑ %</div>
            <div className="col-vat-amount">ΦΠΑ (€)</div>
            <div className="col-total">Τελ. Αξία (€)</div>
          </div>

          {lines.map((line) => (
            <div key={line.id} className="table-row">
              <div className="col-code">{line.line_number}</div>
              <div className="col-description">{line.description}</div>
              <div className="col-quantity">{formatCurrency(line.quantity)}</div>
              <div className="col-unit"></div>
              <div className="col-price">{formatCurrency(line.unit_price)}</div>
              <div className="col-discount">0,00</div>
              <div className="col-value">{formatCurrency(line.net_value)}</div>
              <div className="col-vat-percent">{VAT_RATES[line.vat_category] || `${line.vat_category}%`}</div>
              <div className="col-vat-amount">{formatCurrency(line.vat_amount)}</div>
              <div className="col-total">{formatCurrency(line.net_value + line.vat_amount)}</div>
            </div>
          ))}

          {/* Totals Row */}
          <div className="table-row totals-row">
            <div className="col-code"></div>
            <div className="col-description"><strong>Σύνολα</strong></div>
            <div className="col-quantity"></div>
            <div className="col-unit"></div>
            <div className="col-price"></div>
            <div className="col-discount">0,00</div>
            <div className="col-value"><strong>{formatCurrency(invoice.total_net)}</strong></div>
            <div className="col-vat-percent"></div>
            <div className="col-vat-amount"><strong>{formatCurrency(invoice.total_vat)}</strong></div>
            <div className="col-total"><strong>{formatCurrency(invoice.total_gross)}</strong></div>
          </div>
        </div>

        {/* Summary Section */}
        <div className="summary-section">
          <div className="summary-header">
            <div className="summary-col">Συνολ. Αξία</div>
            <div className="summary-col">(-) Παρακρατούμενοι</div>
            <div className="summary-col">Παρακρατούμενοι (πλφ)</div>
            <div className="summary-col">(-) Κρατήσεις</div>
            <div className="summary-col">Κρατήσεις (πλφ)</div>
            <div className="summary-col">(+) Χαρτόσημο</div>
            <div className="summary-col">(+) Τέλη</div>
            <div className="summary-col">(+) Λοιποί Φόροι</div>
          </div>
          <div className="summary-row">
            <div className="summary-col">{formatCurrency(invoice.total_gross)}</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
          </div>
        </div>

        {/* Total Payable */}
        <div className="total-payable">
          <strong>Πληρωτέο (€): {formatCurrency(invoice.total_gross)}</strong>
        </div>

        {/* Notes Section */}
        <div className="notes-section">
          <div className="notes-label">Παρατηρήσεις:</div>
          <div className="notes-content"></div>
        </div>

        {/* Footer */}
        <div className="footer">
          <div className="page-number">Σελίδα 1 από 1</div>
          <div className="footer-text">
            Η ευθύνη για το περιεχόμενο του παραστατικού ανήκει αποκλειστικά στον εκδότη αυτού
          </div>
          <div className="footer-app">Εκδόθηκε από την εφαρμογή:</div>
        </div>
      </div>

      <style jsx>{`
        .no-print {
          display: block;
        }
        
        @media print {
          .no-print {
            display: none !important;
          }
          
          .greek-invoice {
            width: 100%;
            max-width: none;
            margin: 0;
            padding: 0;
            box-shadow: none;
          }
          
          body {
            margin: 0;
            padding: 0;
          }
        }

        .greek-invoice-container {
          max-width: 210mm;
          margin: 0 auto;
          padding: 20px;
          background: white;
        }

        .greek-invoice {
          width: 100%;
          max-width: 190mm;
          margin: 0 auto;
          background: white;
          font-family: 'Arial', sans-serif;
          font-size: 9px;
          line-height: 1.2;
          color: #000;
        }

        /* Company Header */
        .company-header {
          margin-bottom: 15px;
        }

        .info-row {
          display: flex;
          border: 1px solid #000;
          border-bottom: none;
        }

        .info-row:last-child {
          border-bottom: 1px solid #000;
        }

        .info-label {
          width: 80px;
          padding: 4px 6px;
          border-right: 1px solid #000;
          background-color: #f8f9fa;
          font-weight: bold;
          font-size: 9px;
        }

        .info-value {
          flex: 1;
          padding: 4px 6px;
          font-size: 9px;
        }

        /* Invoice Type Header */
        .invoice-type-header {
          background-color: #4a90e2;
          color: white;
          text-align: center;
          padding: 8px;
          font-size: 11px;
          font-weight: bold;
          margin-bottom: 10px;
        }

        /* Invoice Details */
        .invoice-details {
          display: flex;
          gap: 10px;
          margin-bottom: 10px;
          flex-wrap: wrap;
        }

        .detail-item {
          display: flex;
          align-items: center;
          gap: 5px;
        }

        .detail-label {
          font-size: 9px;
          font-weight: bold;
        }

        .detail-value {
          border: 1px solid #000;
          padding: 2px 6px;
          min-width: 80px;
          text-align: center;
          font-size: 9px;
        }

        /* Client Section */
        .client-section {
          margin-bottom: 15px;
        }

        .client-header {
          font-size: 10px;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .client-row {
          display: flex;
          margin-bottom: 2px;
        }

        .client-label {
          width: 80px;
          font-size: 9px;
          padding: 2px 6px;
        }

        .client-value {
          flex: 1;
          background-color: #e3f2fd;
          padding: 2px 6px;
          font-size: 9px;
        }

        /* Invoice Table */
        .invoice-table {
          margin-bottom: 10px;
        }

        .table-header {
          display: grid;
          grid-template-columns: 6% 30% 10% 8% 12% 10% 12% 8% 10% 14%;
          background-color: #4a90e2;
          color: white;
          font-weight: bold;
          font-size: 9px;
        }

        .table-row {
          display: grid;
          grid-template-columns: 6% 30% 10% 8% 12% 10% 12% 8% 10% 14%;
          border-bottom: 1px solid #ddd;
          font-size: 9px;
        }

        .totals-row {
          background-color: #f8f9fa;
        }

        .table-header > div,
        .table-row > div {
          padding: 4px 2px;
          border-right: 1px solid #ddd;
        }

        .col-code,
        .col-quantity,
        .col-unit,
        .col-vat-percent {
          text-align: center;
        }

        .col-price,
        .col-discount,
        .col-value,
        .col-vat-amount,
        .col-total {
          text-align: right;
        }

        /* Summary Section */
        .summary-section {
          margin-bottom: 15px;
        }

        .summary-header {
          display: grid;
          grid-template-columns: repeat(8, 1fr);
          background-color: #4a90e2;
          color: white;
          font-weight: bold;
          font-size: 9px;
        }

        .summary-row {
          display: grid;
          grid-template-columns: repeat(8, 1fr);
          background-color: #f8f9fa;
          font-size: 9px;
        }

        .summary-header > div,
        .summary-row > div {
          padding: 3px 2px;
          text-align: center;
          border-right: 1px solid #ddd;
        }

        /* Total Payable */
        .total-payable {
          background-color: #e3f2fd;
          padding: 8px;
          text-align: center;
          font-size: 12px;
          font-weight: bold;
          margin-bottom: 15px;
        }

        /* Notes Section */
        .notes-section {
          border: 1px solid #000;
          min-height: 40px;
          margin-bottom: 20px;
          position: relative;
        }

        .notes-label {
          font-size: 9px;
          font-weight: bold;
          padding: 2px 5px;
        }

        .notes-content {
          flex: 1;
          min-height: 30px;
        }

        /* Footer */
        .footer {
          position: relative;
          font-size: 8px;
          color: #666;
        }

        .page-number {
          position: absolute;
          right: 0;
          font-size: 9px;
        }

        .footer-text {
          text-align: center;
          margin-bottom: 5px;
        }

        .footer-app {
          text-align: center;
        }
      `}</style>
    </div>
  );
}